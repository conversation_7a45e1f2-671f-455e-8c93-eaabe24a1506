"""
Simple MCP Server for Railway Deployment

Clean implementation using FastMCP following SDK best practices.
"""

import asyncio
import json
import logging
import os
import sys
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Optional, AsyncIterator

from mcp.server.fastmcp import FastMCP, Context
from starlette.requests import Request
from starlette.responses import JSONResponse

# Import components
try:
    from .tools import register_all_tools
except ImportError:
    from tools import register_all_tools

@asynccontextmanager
async def app_lifespan(server: FastMCP) -> AsyncIterator[None]:
    """Manage the application lifecycle with proper resource initialization."""

    # Setup logging
    log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
    logging.basicConfig(
        level=getattr(logging, log_level),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    logger = logging.getLogger(__name__)
    logger.info("Starting MCP server initialization...")

    # Initialize Hybrid Cache System
    try:
        from .cache import initialize_cache_system
        cache_initialized = await initialize_cache_system()
        if cache_initialized:
            logger.info("🚀 Hybrid cache system initialized successfully")
        else:
            logger.warning("Hybrid cache system failed to initialize - using fallback")
    except Exception as e:
        logger.error(f"Failed to initialize hybrid cache: {e}")

    try:
        yield None
    finally:
        logger.info("MCP server shutdown complete")


# Create a FastMCP server with lifespan management
# Use stateless_http=True for better remote deployment compatibility
mcp = FastMCP(
    "String MCP Server",
    lifespan=app_lifespan,
    stateless_http=True,  # Enable stateless mode for Railway deployment
    json_response=True    # Use JSON responses instead of SSE for better compatibility
)

# Configure mount path for MCP endpoints
mcp.settings.mount_path = "/mcp"

# Standardized API response helper
def create_api_response(data: dict, success: bool = True, status_code: int = 200, request_id: str = None) -> JSONResponse:
    """Create standardized API response format."""
    import uuid
    from datetime import datetime

    if request_id is None:
        request_id = str(uuid.uuid4())

    if success:
        response = {
            "success": True,
            "data": data,
            "meta": {
                "timestamp": datetime.utcnow().isoformat(),
                "version": "v1",
                "request_id": request_id
            }
        }
    else:
        response = {
            "success": False,
            "error": data,
            "meta": {
                "timestamp": datetime.utcnow().isoformat(),
                "version": "v1",
                "request_id": request_id
            }
        }

    # Standard CORS headers
    headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
        "Access-Control-Allow-Headers": "*",
    }

    return JSONResponse(response, status_code=status_code, headers=headers)

# Add CORS middleware for better client compatibility
@mcp.custom_route("/api/v1/mcp/info", methods=["GET"])
async def mcp_info(request: Request) -> JSONResponse:
    """MCP server information endpoint."""
    # Prepare response data
    response_data = {
        "name": "String MCP Server",
        "version": "1.0.0",
        "transport": "streamable-http",
        "capabilities": ["tools", "resources", "prompts"],
        "mount_path": "/mcp",
        "stateless": True,
        "json_response": True,
        "force_json": True
    }

    # Try to add security enhancement
    try:
        # Import security function for minimal integration
        try:
            from .security.simple_security import secure_response
        except ImportError:
            from security.simple_security import secure_response

        # Add minimal security enhancement
        client_ip = request.headers.get('X-Forwarded-For',
                                      request.headers.get('X-Real-IP', 'unknown'))
        secured_data, security_headers = secure_response(response_data, "/api/v1/mcp/info", client_ip)

        return create_api_response(secured_data)

    except ImportError:
        # Fallback if security module not available
        return create_api_response(response_data)

# Configure server settings for Railway deployment
port = os.getenv('PORT')
if port:
    # Set host and port for Railway deployment
    mcp.settings.host = os.getenv('HOST', '0.0.0.0')
    mcp.settings.port = int(port)


# Standardized health check endpoints
@mcp.custom_route("/api/v1/health", methods=["GET"])
async def health_check(request: Request) -> JSONResponse:
    """Health check endpoint for Railway deployment with Redis monitoring."""
    logger = logging.getLogger(__name__)
    logger.info("Health check accessed at /api/v1/health")

    health_data = {
        "status": "healthy",
        "service": "string-mcp-server",
        "path": "/api/v1/health",
        "redis": {"status": "not_configured"}
    }

    # Check Hybrid Cache health
    try:
        from .cache import get_hybrid_cache
        hybrid_cache = await get_hybrid_cache()

        if hybrid_cache.initialized:
            cache_health = await hybrid_cache.health_check()
            cache_stats = await hybrid_cache.get_comprehensive_stats()

            health_data["hybrid_cache"] = {
                "system_health": cache_health["hybrid_system"],
                "redis_coordination": cache_health["redis_coordination"],
                "server_cache": cache_health["server_cache"],
                "fallback_ready": cache_health["fallback_ready"],
                "performance": {
                    "redis_operations": cache_stats["redis_operations"],
                    "server_operations": cache_stats["server_operations"],
                    "hybrid_operations": cache_stats["hybrid_operations"],
                    "fallback_operations": cache_stats["fallback_operations"]
                }
            }

            # Set overall status based on hybrid cache health
            if cache_health["hybrid_system"] == "degraded":
                health_data["status"] = "degraded"
            elif cache_health["hybrid_system"] == "functional":
                health_data["status"] = "healthy"  # Server cache working
            # "optimal" means both Redis and Server working
        else:
            health_data["hybrid_cache"] = {"status": "not_initialized"}
            health_data["status"] = "degraded"
    except Exception as e:
        health_data["hybrid_cache"] = {"status": "error", "error": str(e)}
        health_data["status"] = "degraded"

    # Determine HTTP status code based on health
    status_code = 200 if health_data["status"] in ["healthy", "optimal"] else 503
    return create_api_response(health_data, success=True, status_code=status_code)





# Comprehensive status endpoint for debugging
@mcp.custom_route("/api/v1/status", methods=["GET"])
async def server_status(request: Request) -> JSONResponse:
    """Basic server status - sensitive info removed for security."""
    logger = logging.getLogger(__name__)
    logger.info("Status endpoint accessed at /api/v1/status")

    # Basic status info only - no sensitive data
    status_data = {
        "status": "healthy",
        "service": "string-mcp-server",
        "version": "1.0.0",
        "endpoints": {
            "health": "/api/v1/health",
            "status": "/api/v1/status",
            "mcp_info": "/api/v1/mcp/info",
            "connections": "/api/v1/connections",
            "vscode": "/api/v1/vscode",
            "jobs": "/api/v1/jobs",
            "mcp_protocol": "/mcp/"
        },
        "transport": "streamable-http"
    }

    return create_api_response(status_data)


# Note: API key management tools (create_user, generate_api_key, validate_api_key_tool) 
# have been removed from MCP interface as they should be handled by the web frontend.
# The underlying storage and auth_manager functionality remains available for web API endpoints.


# MCP Resources
@mcp.resource("health://status")
def get_health_status() -> str:
    """Get server health status."""
    try:
        # For resources, we'll use a simple approach without context for now
        status = {
            "status": "healthy",
            "service": "string-mcp-server",
            "timestamp": datetime.utcnow().isoformat(),
            "auth_enabled": True  # Simplified for now
        }

        return str(status)
    except Exception as e:
        return f"Error: {str(e)}"


@mcp.resource("stats://api-keys")
def get_api_key_stats() -> str:
    """Get API key usage statistics."""
    try:
        return "API Key Statistics: Available via tools"
    except Exception as e:
        return f"Error: {str(e)}"


# Register all existing tools from the tools module
def register_existing_tools():
    """Register all tools from the tools module."""
    try:
        register_all_tools(mcp)
    except Exception as e:
        logging.error(f"Failed to register existing tools: {e}")


def register_vscode_integration(auth_manager=None):
    """Register VSCode extension integration endpoints."""
    logger = logging.getLogger(__name__)
    try:
        from .integrations.vscode import add_vscode_integration

        # Add VSCode integration
        vscode_integration = add_vscode_integration(mcp, auth_manager)
        logger.info("VSCode extension integration registered successfully")
        return vscode_integration

    except ImportError as e:
        logger.warning(f"VSCode integration not available: {e}")
        return None
    except Exception as e:
        logger.error(f"Failed to register VSCode integration: {e}")
        return None


def register_api_endpoints():
    """Register simplified API endpoints for connection identification."""
    logger = logging.getLogger(__name__)

    try:
        from .api.endpoints import register_simple_api_endpoints

        # Register the simplified endpoints
        auth_manager = register_simple_api_endpoints(mcp)
        logger.info("Simplified API endpoints registered successfully")
        return auth_manager

    except ImportError as e:
        logger.warning(f"Simple API endpoints not available: {e}")
        return None
    except Exception as e:
        logger.error(f"Failed to register simple API endpoints: {e}")
        return None












    logger.info("API endpoints registered successfully")


def main() -> None:
    """Main entry point for the MCP server."""
    # Setup early logging for startup diagnostics
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    logger = logging.getLogger(__name__)

    try:
        logger.info("=== MCP Server Startup ===")

        # Check if running in Railway and set environment variables early
        port = os.getenv('PORT')
        host = os.getenv('HOST', '0.0.0.0')

        if port:
            logger.info(f"Railway deployment detected - Port: {port}, Host: {host}")

            # Ensure we bind to the correct host and port for Railway
            os.environ['FASTMCP_SERVER_HOST'] = host
            os.environ['FASTMCP_SERVER_PORT'] = port

            # Also set uvicorn-specific environment variables as backup
            os.environ['UVICORN_HOST'] = host
            os.environ['UVICORN_PORT'] = port

            logger.info(f"Set server binding: {host}:{port}")

            # Log mount path configuration
            mount_path = os.getenv('FASTMCP_SETTINGS_MOUNT_PATH', '/mcp')
            logger.info(f"FastMCP mount path: {mount_path}")
        else:
            logger.info("Local development mode detected")

        # Register existing tools
        logger.info("Registering MCP tools...")
        register_existing_tools()
        logger.info("MCP tools registration completed")

        # Register API endpoints
        logger.info("Registering API endpoints...")
        register_api_endpoints()
        logger.info("API endpoints registration completed")

        # Register VSCode integration
        logger.info("Registering VSCode extension integration...")
        vscode_integration = register_vscode_integration(None)  # No auth manager needed
        if vscode_integration:
            logger.info("VSCode extension integration completed")
        else:
            logger.info("VSCode extension integration skipped")

        # Log all available endpoints
        logger.info("Available standardized API endpoints:")
        logger.info("Health & Status:")
        logger.info("  - GET /api/v1/health (Railway health check)")
        logger.info("  - GET /api/v1/status (comprehensive diagnostics)")
        logger.info("  - GET /api/v1/mcp/info (MCP server information)")
        logger.info("Connection Management:")
        logger.info("  - POST /api/v1/connections (create mcp_ key)")
        logger.info("  - POST /api/v1/connections/validate (validate mcp_ key)")
        logger.info("  - POST /api/v1/connections/regenerate (regenerate mcp_ key)")
        logger.info("  - GET /api/v1/connections (list connections)")
        logger.info("  - GET /api/v1/connections/stats (connection statistics)")
        if vscode_integration:
            logger.info("VSCode Integration:")
            logger.info("  - POST /api/v1/vscode/chunks (receive code chunks)")
            logger.info("  - GET /api/v1/vscode/status (indexing status)")
            logger.info("  - POST /api/v1/vscode/cleanup (cleanup temp files)")
            logger.info("Job Management:")
            logger.info("  - GET /api/v1/jobs (list user jobs)")
            logger.info("  - GET /api/v1/jobs/{id} (get job status)")
            logger.info("  - GET /api/v1/jobs/stats (job statistics)")
        logger.info("MCP Protocol:")
        logger.info("  - /mcp/* (MCP protocol endpoints)")

        # Run server
        if port:
            logger.info(f"Starting FastMCP server with streamable-http transport on {host}:{port}...")
            logger.info(f"MCP endpoints available at: http://{host}:{port}/mcp")
            logger.info("Server configured for stateless HTTP with JSON responses")
            # Railway deployment - run as a streamable HTTP server
            mcp.run(transport="streamable-http")
        else:
            logger.info("Starting FastMCP server with stdio transport...")
            # Local development - run as stdio server
            mcp.run()

    except Exception as e:
        logger.error(f"Failed to start MCP server: {e}")
        logger.error(f"Exception details: {type(e).__name__}: {str(e)}")

        # Print additional debugging information
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")

        # Try to identify common issues
        if "Permission denied" in str(e):
            logger.error("DIAGNOSIS: Permission denied - check file/directory permissions")
        elif "Address already in use" in str(e):
            logger.error("DIAGNOSIS: Port already in use - check PORT environment variable")
        elif "No module named" in str(e):
            logger.error("DIAGNOSIS: Missing Python module - check dependencies")
        elif "database" in str(e).lower():
            logger.error("DIAGNOSIS: Database issue - check database path and permissions")

        sys.exit(1)


if __name__ == "__main__":
    main()