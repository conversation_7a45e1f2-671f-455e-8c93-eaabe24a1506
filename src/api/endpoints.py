"""
Standardized API Endpoints for Connection Management

RESTful endpoints following API best practices for managing mcp_ API keys
and VSCode extension connection identification.
"""

import json
import logging
import uuid
from datetime import datetime
from typing import Dict, Any

from starlette.requests import Request
from starlette.responses import JSONResponse

from ..auth import get_simple_auth_manager, validate_connection_key


def create_api_response(data: dict, success: bool = True, status_code: int = 200, request_id: str = None) -> JSONResponse:
    """Create standardized API response format."""
    if request_id is None:
        request_id = str(uuid.uuid4())

    if success:
        response = {
            "success": True,
            "data": data,
            "meta": {
                "timestamp": datetime.utcnow().isoformat(),
                "version": "v1",
                "request_id": request_id
            }
        }
    else:
        response = {
            "success": False,
            "error": data,
            "meta": {
                "timestamp": datetime.utcnow().isoformat(),
                "version": "v1",
                "request_id": request_id
            }
        }

    # Standard CORS headers
    headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
        "Access-Control-Allow-Headers": "*",
    }

    return JSONResponse(response, status_code=status_code, headers=headers)


def register_simple_api_endpoints(mcp_server):
    """Register standardized API endpoints with the MCP server."""
    logger = logging.getLogger(__name__)
    auth_manager = get_simple_auth_manager()

    @mcp_server.custom_route("/api/v1/connections", methods=["POST"])
    async def create_connection(request: Request) -> JSONResponse:
        """
        Create a new connection (user + API key) for VSCode extension.

        This is the main endpoint for setting up a new VSCode extension connection.
        """
        try:
            body = await request.body()
            data = json.loads(body)

            identifier = data.get("identifier")  # email, username, etc.
            name = data.get("name")  # display name

            if not identifier or not name:
                error_data = {
                    "code": "MISSING_REQUIRED_FIELDS",
                    "message": "identifier and name are required",
                    "details": {"required_fields": ["identifier", "name"]}
                }
                return create_api_response(error_data, success=False, status_code=400)

            # Check if user already exists
            existing_user = auth_manager.get_user_by_identifier(identifier)
            if existing_user:
                error_data = {
                    "code": "CONNECTION_ALREADY_EXISTS",
                    "message": f"User '{identifier}' already has an API key",
                    "details": {
                        "existing_key_prefix": existing_user.api_key[:8] + "..." if existing_user.api_key else None
                    }
                }
                return create_api_response(error_data, success=False, status_code=409)

            # Create new user with API key
            user = auth_manager.create_user_with_key(identifier, name)
            if user:
                response_data = {
                    "connection": {
                        "user_id": user.id,
                        "identifier": user.identifier,
                        "name": user.name,
                        "api_key": user.api_key,
                        "created_at": user.created_at.isoformat()
                    },
                    "setup_instructions": {
                        "vscode_settings": {
                            "mcp.url": "https://mcp.rabtune.com",
                            "mcp.apiKey": user.api_key,
                            "mcp.maxChunkSize": 2000
                        },
                        "note": "Add these settings to your VSCode settings.json"
                    }
                }
                return create_api_response(response_data, status_code=201)
            else:
                error_data = {
                    "code": "CONNECTION_CREATION_FAILED",
                    "message": "Failed to create connection",
                    "details": {}
                }
                return create_api_response(error_data, success=False, status_code=500)

        except json.JSONDecodeError:
            error_data = {
                "code": "INVALID_JSON",
                "message": "Request body must be valid JSON",
                "details": {}
            }
            return create_api_response(error_data, success=False, status_code=400)
        except Exception as e:
            logger.error(f"Error creating connection: {e}")
            error_data = {
                "code": "INTERNAL_SERVER_ERROR",
                "message": "An unexpected error occurred",
                "details": {"error": str(e)}
            }
            return create_api_response(error_data, success=False, status_code=500)

    @mcp_server.custom_route("/api/v1/connections/validate", methods=["POST"])
    async def validate_connection(request: Request) -> JSONResponse:
        """Validate a connection API key."""
        try:
            body = await request.body()
            data = json.loads(body)

            api_key = data.get("api_key")
            if not api_key:
                error_data = {
                    "code": "MISSING_API_KEY",
                    "message": "api_key is required",
                    "details": {"required_fields": ["api_key"]}
                }
                return create_api_response(error_data, success=False, status_code=400)

            # Validate the connection
            user = auth_manager.validate_api_key(api_key)
            if user:
                response_data = {
                    "valid": True,
                    "connection": {
                        "user_id": user.id,
                        "identifier": user.identifier,
                        "name": user.name,
                        "created_at": user.created_at.isoformat(),
                        "last_used": user.last_used.isoformat() if user.last_used else None,
                        "usage_count": user.usage_count
                    }
                }
                return create_api_response(response_data)
            else:
                error_data = {
                    "code": "INVALID_API_KEY",
                    "message": "API key not found or invalid format",
                    "details": {"api_key_prefix": api_key[:8] + "..." if len(api_key) > 8 else api_key}
                }
                return create_api_response(error_data, success=False, status_code=401)

        except json.JSONDecodeError:
            error_data = {
                "code": "INVALID_JSON",
                "message": "Request body must be valid JSON",
                "details": {}
            }
            return create_api_response(error_data, success=False, status_code=400)
        except Exception as e:
            logger.error(f"Error validating connection: {e}")
            error_data = {
                "code": "INTERNAL_SERVER_ERROR",
                "message": "An unexpected error occurred",
                "details": {"error": str(e)}
            }
            return create_api_response(error_data, success=False, status_code=500)

    @mcp_server.custom_route("/api/v1/connections/regenerate", methods=["POST"])
    async def regenerate_key(request: Request) -> JSONResponse:
        """Regenerate API key for an existing connection."""
        try:
            body = await request.body()
            data = json.loads(body)

            identifier = data.get("identifier")
            if not identifier:
                error_data = {
                    "code": "MISSING_IDENTIFIER",
                    "message": "identifier is required",
                    "details": {"required_fields": ["identifier"]}
                }
                return create_api_response(error_data, success=False, status_code=400)

            # Check if user exists
            user = auth_manager.get_user_by_identifier(identifier)
            if not user:
                error_data = {
                    "code": "CONNECTION_NOT_FOUND",
                    "message": f"Connection not found for identifier: {identifier}",
                    "details": {"identifier": identifier}
                }
                return create_api_response(error_data, success=False, status_code=404)

            # Regenerate API key
            new_api_key = auth_manager.regenerate_api_key(identifier)
            if new_api_key:
                response_data = {
                    "connection": {
                        "user_id": user.id,
                        "identifier": user.identifier,
                        "name": user.name,
                        "new_api_key": new_api_key
                    },
                    "setup_instructions": {
                        "vscode_settings": {
                            "mcp.url": "https://mcp.rabtune.com",
                            "mcp.apiKey": new_api_key,
                            "mcp.maxChunkSize": 2000
                        },
                        "note": "Update your VSCode settings with the new API key"
                    }
                }
                return create_api_response(response_data)
            else:
                error_data = {
                    "code": "KEY_REGENERATION_FAILED",
                    "message": "Failed to regenerate API key",
                    "details": {"identifier": identifier}
                }
                return create_api_response(error_data, success=False, status_code=500)

        except json.JSONDecodeError:
            error_data = {
                "code": "INVALID_JSON",
                "message": "Request body must be valid JSON",
                "details": {}
            }
            return create_api_response(error_data, success=False, status_code=400)
        except Exception as e:
            logger.error(f"Error regenerating key: {e}")
            error_data = {
                "code": "INTERNAL_SERVER_ERROR",
                "message": "An unexpected error occurred",
                "details": {"error": str(e)}
            }
            return create_api_response(error_data, success=False, status_code=500)

    @mcp_server.custom_route("/api/v1/connections", methods=["GET"])
    async def list_connections(request: Request) -> JSONResponse:
        """List all connections (for admin purposes)."""
        try:
            users = auth_manager.get_all_users()

            connections = []
            for user in users:
                connections.append({
                    "user_id": user.id,
                    "identifier": user.identifier,
                    "name": user.name,
                    "created_at": user.created_at.isoformat(),
                    "last_used": user.last_used.isoformat() if user.last_used else None,
                    "usage_count": user.usage_count,
                    "api_key_prefix": user.api_key[:8] + "..." if user.api_key else None
                })

            response_data = {
                "connections": connections,
                "total_count": len(connections)
            }
            return create_api_response(response_data)

        except Exception as e:
            logger.error(f"Error listing connections: {e}")
            error_data = {
                "code": "INTERNAL_SERVER_ERROR",
                "message": "An unexpected error occurred",
                "details": {"error": str(e)}
            }
            return create_api_response(error_data, success=False, status_code=500)

    @mcp_server.custom_route("/api/v1/connections/stats", methods=["GET"])
    async def get_connection_stats(request: Request) -> JSONResponse:
        """Get connection statistics."""
        try:
            stats = auth_manager.get_stats()

            response_data = {
                "stats": stats,
                "server_info": {
                    "service": "string-mcp-server",
                    "version": "1.0.0",
                    "auth_type": "simple_identification"
                }
            }
            return create_api_response(response_data)

        except Exception as e:
            logger.error(f"Error getting stats: {e}")
            error_data = {
                "code": "INTERNAL_SERVER_ERROR",
                "message": "An unexpected error occurred",
                "details": {"error": str(e)}
            }
            return create_api_response(error_data, success=False, status_code=500)

    logger.info("Standardized API endpoints registered successfully")
    return auth_manager


def simple_auth_middleware(api_key: str) -> bool:
    """
    Simple middleware for validating connection API keys.
    
    This is used by the VSCode integration endpoints to validate
    that the request is coming from a known VSCode extension.
    
    Args:
        api_key: API key from request
        
    Returns:
        True if valid connection, False otherwise
    """
    return validate_connection_key(api_key)
