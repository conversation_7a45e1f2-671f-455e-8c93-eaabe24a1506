[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[project]
name = "string-mcp-secure"
version = "0.2.0"
description = "Secure MCP hosting solution with upload-based file processing, credential scanning, and sandboxed execution"
readme = "README.md"
requires-python = ">=3.11"
license = {text = "MIT"}
authors = [
    {name = "MCP Security Team", email = "<EMAIL>"},
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Security",
    "Topic :: Software Development :: Code Generators",
    "Topic :: System :: Systems Administration",
]

dependencies = [
    # Core MCP and FastAPI
    "mcp>=1.9.0",
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    # Original MCP tools dependencies
    "networkx>=3.0",
    "tree-sitter>=0.20.0",
    "tree-sitter-python>=0.20.0",
    # Security and authentication
    "cryptography>=41.0.0",
    "python-multipart>=0.0.6",
    "passlib[bcrypt]>=1.7.4",
    "python-jose[cryptography]>=3.3.0",
    "bcrypt>=4.1.0",
    # System monitoring
    "psutil>=5.9.0",
    # File processing and validation
    "python-magic>=0.4.27",
    "chardet>=5.2.0",
    "filetype>=1.2.0",
    "python-magic-bin>=0.4.14; sys_platform == 'win32'",
    # HTTP clients and CORS
    "httpx>=0.25.0",
    "requests>=2.31.0",
    # Vector database and embeddings (lightweight)
    "qdrant-client>=1.14.2",
    "openai>=1.83.0",
    "numpy>=1.24.0",
    # Logging and monitoring
    "structlog>=23.2.0",
    "python-json-logger>=2.0.0",
    # Rate limiting
    "slowapi>=0.1.9",
    # Async support
    "aiofiles>=23.2.1",
    "async-timeout>=4.0.3",
    # Configuration
    "python-dotenv>=1.0.0",
    # Data processing
    "orjson>=3.9.0",
    "jsonschema>=4.20.0",
    # Security scanning (essential only)
    "regex>=2023.10.3",
    "bandit>=1.7.5",
    # Utilities
    "click>=8.1.0",
    "typing-extensions>=4.8.0",
    "python-dateutil>=2.8.0",
    # Caching and performance
    "cachetools>=5.3.0",
    "redis>=5.0.0",  # Redis for hybrid cache system
    # Network security
    "certifi>=2023.11.17",
    "urllib3>=2.1.0",
    # Error tracking
    "sentry-sdk[fastapi]>=1.38.0",
    "aiohttp>=3.12.7",
]

[project.scripts]
string-mcp = "src.server:main"
string-mcp-secure = "src.api.upload_handler:main"
string-mcp-client = "src.string_mcp.client:main"

[project.optional-dependencies]
# Development dependencies are managed in [tool.uv] section

monitoring = [
    "prometheus-client>=0.19.0",
    "py-spy>=0.3.14",
    "memory-profiler>=0.61.0",
    "pympler>=0.9",
]

notifications = [
    "aiosmtplib>=3.0.0",
    "slack-sdk>=3.26.0",
]

storage = [
    "redis>=5.0.0",
]

docs = [
    "pypdf>=3.17.0",
    "python-docx>=1.1.0",
]

analysis = [
    "scapy>=2.5.0",
    "python-json-logger>=2.0.0",
]

deployment = [
    "supervisor>=4.2.5",
]

[project.urls]
Homepage = "https://github.com/mcp-security/string-mcp-secure"
Repository = "https://github.com/mcp-security/string-mcp-secure"
Issues = "https://github.com/mcp-security/string-mcp-secure/issues"
Documentation = "https://string-mcp-secure.readthedocs.io/"
Security = "https://github.com/mcp-security/string-mcp-secure/security"

[tool.uv]
dev-dependencies = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.0",
    "pre-commit>=3.6.0",
]

# Optimize dependency resolution
resolution = "highest"
prerelease = "disallow"

[tool.uv.sources]
# Pin security-critical dependencies to ensure consistent builds
# torch = { url = "https://download.pytorch.org/whl/torch-2.1.0%2Bcpu-cp312-cp312-win_amd64.whl" }

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | \.uv-cache
  | build
  | dist
  | docker
  | deploy
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "tree_sitter.*",
    "networkx.*",
    "docker.*",
    "yara.*",
    "pefile.*",
    "scapy.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "security: marks tests as security-related",
    "sandbox: marks tests that require sandboxing",
    "job_system: marks tests for job completion system",
    "vector_store: marks tests for vector store functionality",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*",
    "*/.uv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.bandit]
exclude_dirs = ["tests", "test_projects"]
skips = ["B101", "B601"]  # Skip assert_used and shell_injection for tests

[tool.bandit.assert_used]
skips = ["*_test.py", "test_*.py"]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503", "E501"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    ".uv",
    "docker",
    "deploy",
]

[tool.security]
# Security-specific configuration
scan_paths = ["src"]
exclude_paths = ["tests", "test_projects", "examples"]
credential_patterns = [
    "password",
    "secret",
    "token",
    "key",
    "api_key",
    "private_key",
    "access_token",
    "refresh_token",
]

[tool.deployment]
# Deployment configuration for Railway/Docker
health_check_path = "/health"
metrics_path = "/metrics"
port = 8000
workers = 1
max_requests = 1000
max_requests_jitter = 50
timeout = 30
keep_alive = 2

[tool.security.scanning]
# Security scanning configuration
max_file_size = "10MB"
allowed_extensions = [".py", ".txt", ".md", ".json", ".yaml", ".yml", ".toml"]
blocked_extensions = [".exe", ".bat", ".sh", ".ps1", ".dll", ".so"]
scan_timeout = 30
quarantine_suspicious = true

[tool.cloudflare]
# Cloudflare R2 configuration template
bucket_name = "mcp-secure-uploads"
region = "auto"
max_upload_size = "50MB"
retention_days = 30
encryption = "AES256"
